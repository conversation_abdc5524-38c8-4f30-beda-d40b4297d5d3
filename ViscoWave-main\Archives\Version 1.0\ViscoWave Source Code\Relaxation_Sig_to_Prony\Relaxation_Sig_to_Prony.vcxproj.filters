﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="alglibinternal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="alglibmisc.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ap.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="dataanalysis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="diffequations.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="fasttransforms.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="integration.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="interpolation.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="linalg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="optimization.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Relaxation_Sig_to_Prony.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="solvers.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="specialfunctions.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="statistics.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="alglibinternal.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="alglibmisc.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="ap.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="dataanalysis.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="diffequations.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="fasttransforms.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="integration.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="interpolation.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="linalg.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="optimization.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="solvers.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="specialfunctions.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="statistics.cpp">
      <Filter>Resource Files</Filter>
    </ClCompile>
    <ClCompile Include="Relaxation_Sig_to_Prony.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="Relaxation_Sig_to_Prony.def">
      <Filter>Source Files</Filter>
    </None>
  </ItemGroup>
</Project>