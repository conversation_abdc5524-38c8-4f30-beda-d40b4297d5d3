﻿  alglibinternal.cpp
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\alglibinternal.cpp(8331,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  alglibmisc.cpp
  ap.cpp
  Complex_1D_Array.cpp
  Complex_2D_Array.cpp
  dataanalysis.cpp
  diffequations.cpp
  fasttransforms.cpp
  GlobalStiffMatrix.cpp
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\integration.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'GlobalStiffMatrix.cpp')
  
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\GlobalStiffMatrix.cpp(41,32): warning C4101: 'layer_pavement_structure': unreferenced local variable
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\GlobalStiffMatrix.cpp(122,27): warning C4244: 'initializing': conversion from 'alglib::ae_int_t' to 'int', possible loss of data
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\GlobalStiffMatrix.cpp(122,17): warning C4244: 'initializing': conversion from 'alglib::ae_int_t' to 'const int', possible loss of data
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\GlobalStiffMatrix.cpp(123,27): warning C4244: 'initializing': conversion from 'alglib::ae_int_t' to 'int', possible loss of data
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\GlobalStiffMatrix.cpp(123,17): warning C4244: 'initializing': conversion from 'alglib::ae_int_t' to 'const int', possible loss of data
  integration.cpp
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\integration.cpp(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\integration.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'integration.cpp')
  
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\integration.cpp(682,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\integration.cpp(1622,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  interpolation.cpp
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\integration.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'interpolation.cpp')
  
  linalg.cpp
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\linalg.cpp(8130,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\linalg.cpp(8791,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\linalg.cpp(10109,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\linalg.cpp(39467,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\linalg.cpp(43617,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  optimization.cpp
  solvers.cpp
  specialfunctions.cpp
  statistics.cpp
  ViscoWave.cpp
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\integration.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'ViscoWave.cpp')
  
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ViscoWave.cpp(49,53): warning C4101: 'jj': unreferenced local variable
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ViscoWave.cpp(49,35): warning C4101: 'i': unreferenced local variable
  ViscoWaveEngine.cpp
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\integration.h(1,1): warning C4819: The file contains a character that cannot be represented in the current code page (936). Save the file in Unicode format to prevent data loss
  (compiling source file 'ViscoWaveEngine.cpp')
  
     Creating library D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\x64\Release\ViscoWave_x64.lib and object D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\x64\Release\ViscoWave_x64.exp
  Previous IPDB was built with incompatible compiler, fall back to full compilation.
ViscoWave_x64.exp : warning LNK4070: /OUT:ViscoWave.dll directive in .EXP differs from output filename 'D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\x64\Release\ViscoWave_x64.dll'; ignoring directive
  Generating code
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5252): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\ViscoWave\ap.cpp(5230): warning C4723: potential divide by 0
  All 1071 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finished generating code
ViscoWave_x64.exp : warning LNK4070: /OUT:ViscoWave.dll directive in .EXP differs from output filename 'D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\x64\Release\ViscoWave_x64.dll'; ignoring directive
  ViscoWave.vcxproj -> D:\dev\2025\cpp_code\666\333-0.7-参数化处理\11\ViscoWave-main\ViscoWave Source Code\x64\Release\ViscoWave_x64.dll
