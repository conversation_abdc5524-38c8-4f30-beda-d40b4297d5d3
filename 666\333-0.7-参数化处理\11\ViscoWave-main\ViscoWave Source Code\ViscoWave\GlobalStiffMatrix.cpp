﻿#include "GlobalStiffMatrix.h"

#include <cmath>
#include <omp.h>
#include <stdlib.h>
#include <stdio.h>
#include <math.h>
#include <complex>
#include <vector>
#include "Complex_2D_Array.h"
#include "ViscoWaveEngine.h"
#include <algorithm>
#include "alglibinternal.h"
#include <numeric>
#include <iostream>   // 标准输入输出
#include <iomanip>    // 格式化输出控制
#include <iostream>
#include <fstream>
#include <windows.h>



GlobalStiffMatrix::GlobalStiffMatrix(      //初始化全局刚度矩阵的维度，并分配内存
	int num_pavement_layers,               //路面层数
	int num_prony_elements,                //Prony 级数的项数
	int num_VE_layer)                      //粘弹性层的数量
{
	number_pavement_layers = num_pavement_layers;
	number_prony_elements = num_prony_elements;
	number_ve_layer = num_VE_layer;
	matrix = new Complex_2D_Array(2 * number_pavement_layers, 2 * number_pavement_layers);
};//创建 Complex_2D_Array 对象，表示全局刚度矩阵，维度为 2N × 2N（N 为路面层数），每层对应 2个自由度（位移和应力）

void GlobalStiffMatrix::CalculateMatrix(          //根据路面结构参数、Prony 系数、拉普拉斯变量和高斯积分点，计算全局刚度矩阵
	alglib::real_1d_array  pavement_structure,    //路面结构参数数组，每层包含 5 个参数（厚度、泊松比、密度、剪切模量、阻尼
	alglib::real_1d_array  final_prony_values,    //Prony 系数数组，用于描述粘弹性材料的松弛行为
	alglib::complex  laplace_var,                 //拉普拉斯变量（复数），用于频域分析
	double gaussian_point)                        //高斯积分点，用于数值积分
{
	matrix->Reset();                              //高斯积分点，用于数值积分
	struct LayerPavementStructure layer_pavement_structure;           //提取当前层的参数（厚度、泊松比、密度、剪切模量、阻尼）
	for (int layer_number = 0; layer_number < number_pavement_layers; layer_number++)   
	{
		// 计算参数偏移量
		int offset;
		if (layer_number < number_pavement_layers - 1) {
			// 前 N-1 层，每层5参数
			offset = 5 * layer_number;
		}
		else {
			// 最后一层（第三层），前5参数偏移量为 5*(总层数-1)
			offset = 5 * (number_pavement_layers - 1);
		}
		// 提取参数
		LayerPavementStructure layer_pavement_structure;
		if (layer_number == number_pavement_layers - 1) {
			// 第三层：读取6参数（包括饱和度）
			layer_pavement_structure = {
				pavement_structure[offset],          // 厚度
				pavement_structure[offset + 1],      // 泊松比
				pavement_structure[offset + 2],      // 密度
				pavement_structure[offset + 3],      // 剪切模量
				pavement_structure[offset + 4],      // 阻尼
				pavement_structure[offset + 5]       // 饱和度
			};
		}
		else {
			// 其他层：读取5参数，饱和度设为默认值0.0
			layer_pavement_structure = {
				pavement_structure[offset],          // 厚度
				pavement_structure[offset + 1],      // 泊松比
				pavement_structure[offset + 2],      // 密度
				pavement_structure[offset + 3],      // 剪切模量
				pavement_structure[offset + 4],      // 阻尼
				0.0                                  // 默认饱和度
			};

		};
		//根据层类型调用不同的函数计算局部刚度矩阵
		if (layer_number >= 0 && layer_number < number_pavement_layers - 1) {   //中间层、如果剪切模量为 0，调用 stiff2node_visco 计算粘弹性层的刚度矩阵
			if (layer_pavement_structure.shaer_mod == 0) {
				stiff2node_visco(layer_pavement_structure, final_prony_values, laplace_var, gaussian_point, layer_number);
			}
			else {        // 弹性层
				stiff2node_elastic(layer_pavement_structure, laplace_var, gaussian_point, layer_number);//否则，调用 stiff2node_elastic 计算弹性层的刚度矩阵
			}
		}
		else // (layer_number == (num_pavement_layers - 1))   底层  如果厚度为 0，调用 stiff1node_elastic 计算半空间层的刚度矩阵
		{
			if (layer_pavement_structure.thickness == 0) {        //半空间
				stiff1node_elastic(layer_pavement_structure, laplace_var, gaussian_point, layer_number);
			}
			else {            //否则，调用 stiff1node_elastic_nohalf 计算有限厚度底层的刚度矩阵
				stiff1node_elastic_nohalf(layer_pavement_structure, laplace_var, gaussian_point, layer_number);
			}
		}
	}
}

void GlobalStiffMatrix::WriteMatrixToFile(
	const alglib::complex_2d_array& mat,
	const std::string& filename,
	int layer_num,
	double gaussian_point,
	bool is_elastic)
{
	// 打开文件（追加模式）
	std::ofstream outfile(filename, std::ios::app);
	if (!outfile.is_open()) {
		std::cerr << "Error: Failed to open file '" << filename
			<< "' for writing." << std::endl;
		return;
	}
	// 写入层信息
	outfile << "\n============================================\n"
		<< "Layer: " << layer_num << "\n"
		<< "Gaussian Point: " << std::fixed << std::setprecision(6)
		<< gaussian_point << "\n"
		<< "Type: " << (is_elastic ? "Elastic" : "Unsaturated Soil") << "\n"
		<< "Matrix Elements:\n";
	// 写入矩阵元素
	const int rows = mat.rows();
	const int cols = mat.cols();
	for (int i = 0; i < rows; ++i) {
		for (int j = 0; j < cols; ++j) {
			outfile << std::scientific << std::setprecision(6)
				<< "(" << mat[i][j].x << ", "
				<< mat[i][j].y << "i)\t";
		}
		outfile << "\n";
	}
	outfile.close();
}

GlobalStiffMatrix::~GlobalStiffMatrix()
{
}

void GlobalStiffMatrix::stiff2node_visco(            // stiff2node_visco 该函数用于计算粘弹性层的局部刚度矩阵。代码的核心是通过 Prony 级数描述粘弹性材料的松弛行为，并结合 Hankel 变换和拉普拉斯变换求解频域刚度矩阵
	LayerPavementStructure layer_structure,        //当前层的路面结构参数（厚度、泊松比、密度、剪切模量、阻尼）
	alglib::real_1d_array final_prony_values,      //Prony 系数数组，描述粘弹性材料的松弛行为
	alglib::complex Laplace_Var,                  //拉普拉斯变量（复数），用于频域分析
	double gaussian_point,                         //高斯积分点，用于数值积分
	int Layer_Number)                              //当前层的编号
{
	
	alglib::complex Prony_Coeff_E, Prony_Coeff_Rho, Complex_One;
	alglib::complex Hankel_Var, Complex_Poisson, Complex_Density, Complex_Thickness, Complex_Damping;
	alglib::complex Complex_Shear_Mod;
	alglib::complex Complex_Lame;
	alglib::complex C_Wave_Vel;
	alglib::complex S_Wave_Vel;
	alglib::complex Function_F, ehf;
	alglib::complex Function_G, ehg;
	alglib::complex myK, myK1, myQ1, myQ2, myQ3, myQ4, mydenom;
	alglib::complex Ctemp;

	int Num_Pavt_Layers2 = 2 * number_pavement_layers;

	alglib::complex_2d_array c2;

	Complex_One.x = 1;     //复数 1
	Complex_One.y = 0;

	Hankel_Var.x = gaussian_point;       //Hankel 变换变量，实部为高斯积分点，虚部为 0。
	Hankel_Var.y = 0;

	Complex_Poisson.x = layer_structure.possion;
	Complex_Poisson.y = 0;

	Complex_Density.x = layer_structure.density;
	Complex_Density.y = 0;

	Complex_Thickness.x = layer_structure.thickness;
	Complex_Thickness.y = 0;

	Complex_Damping.x = layer_structure.damping;
	Complex_Damping.y = 0;

	for (int i = 0; i < number_prony_elements; i++) {
		if (i == 0) {
			Complex_Shear_Mod.x = final_prony_values[Layer_Number];     //复数剪切模量，通过 Prony 级数计算
			Complex_Shear_Mod.y = 0;
		}
		else {
			Prony_Coeff_E.x = final_prony_values[(number_ve_layer + 1) * i + Layer_Number];
			Prony_Coeff_E.y = 0;
			Prony_Coeff_Rho.x = final_prony_values[(number_ve_layer + 1) * i + number_ve_layer];
			Prony_Coeff_Rho.y = 0;
			Complex_Shear_Mod += Prony_Coeff_E * Laplace_Var / (Laplace_Var + Complex_One / Prony_Coeff_Rho);
		}
	}
	Complex_Shear_Mod *= (1 + Complex_Damping * Laplace_Var);            //阻尼修正

	Complex_Lame = 2 * Complex_Shear_Mod * Complex_Poisson / (1 - 2 * Complex_Poisson);
	C_Wave_Vel = ViscoWaveEngine::Compute_Power(((Complex_Lame + 2 * Complex_Shear_Mod) / Complex_Density), 0.5);    //压缩波速
	S_Wave_Vel = ViscoWaveEngine::Compute_Power((Complex_Shear_Mod / Complex_Density), 0.5);                         //剪切波速

	alglib::complex Hankel_Var_Square = Hankel_Var * Hankel_Var;
	alglib::complex Laplace_Var_Square = Laplace_Var * Laplace_Var;
	Function_F = ViscoWaveEngine::Compute_Power((Hankel_Var_Square + (Laplace_Var_Square / (C_Wave_Vel * C_Wave_Vel))), 0.5);
	Function_G = ViscoWaveEngine::Compute_Power((Hankel_Var_Square + (Laplace_Var_Square / (S_Wave_Vel * S_Wave_Vel))), 0.5);
     //Function_F、Function_G：波数域的衰减函数 
	ehf = ViscoWaveEngine::Compute_Exp(-Complex_Thickness * Function_F);
	ehg = ViscoWaveEngine::Compute_Exp(-Complex_Thickness * Function_G);
	//  ehf、ehg：指数衰减项
	alglib::complex Function_F_Square = Function_F * Function_F;
	alglib::complex Function_G_Square = Function_G * Function_G;
	alglib::complex Function_F_Mul_Function_G = Function_F * Function_G;

	myK = Hankel_Var_Square + Function_G_Square;
	myK1 = Hankel_Var_Square - Function_G_Square;

	alglib::complex ehf_square = ehf * ehf;
	alglib::complex ehg_square = ehg * ehg;
	myQ1 = 1 - ehf_square;
	myQ2 = 1 - ehg_square;
	myQ3 = 1 + ehf_square;
	myQ4 = 1 + ehg_square;
	mydenom = myQ1 * myQ2 * (Function_F_Square * Function_G_Square + Hankel_Var_Square * Hankel_Var_Square)
		- 2 * (myQ3 * myQ4 - 4 * ehf * ehg) * Function_F_Mul_Function_G * Hankel_Var_Square;

	c2.setlength(4, 4);
	c2(0, 0) = Complex_Shear_Mod / mydenom * (-Function_F * myK1 * (myQ1 * myQ4 * Function_F_Mul_Function_G - myQ2 * myQ3 * Hankel_Var_Square));
	c2(0, 1) = Complex_Shear_Mod / mydenom * (-Hankel_Var * (-myQ1 * myQ2 * (2 * Function_F_Square * Function_G_Square + Hankel_Var_Square * myK)
		+ (myQ3 * myQ4 - 4 * ehf * ehg) * Function_F_Mul_Function_G * (Function_G_Square + 3 * Hankel_Var_Square)));
	c2(0, 2) = Complex_Shear_Mod / mydenom * (2 * Function_F * myK1 * (myQ1 * ehg * Function_F_Mul_Function_G - ehf * myQ2 * Hankel_Var_Square));
	c2(0, 3) = Complex_Shear_Mod / mydenom * (-2 * (ehf - ehg) * (1 - ehf * ehg) * Function_F_Mul_Function_G * Hankel_Var * myK1);

	c2(1, 0) = c2(0, 1);
	c2(1, 1) = Complex_Shear_Mod / mydenom * (-Function_G * myK1 * (myQ2 * myQ3 * Function_F_Mul_Function_G - myQ1 * myQ4 * Hankel_Var_Square));
	c2(1, 2) = Complex_Shear_Mod / mydenom * (2 * (ehf - ehg) * (1 - ehf * ehg) * Function_F_Mul_Function_G * Hankel_Var * (Hankel_Var_Square - Function_G_Square));
	c2(1, 3) = Complex_Shear_Mod / mydenom * (-2 * Function_G * myK1 * (-ehf * myQ2 * Function_F_Mul_Function_G + myQ1 * ehg * Hankel_Var_Square));

	c2(2, 0) = c2(0, 2);
	c2(2, 1) = c2(1, 2);
	c2(2, 2) = Complex_Shear_Mod / mydenom * (-Function_F * myK1 * (myQ1 * myQ4 * Function_F_Mul_Function_G - myQ2 * myQ3 * Hankel_Var_Square));
	c2(2, 3) = Complex_Shear_Mod / mydenom * (Hankel_Var * (-myQ1 * myQ2 * (2 * Function_F_Square * Function_G_Square + Hankel_Var_Square * myK)
		+ (myQ3 * myQ4 - 4 * ehf * ehg) * Function_F_Mul_Function_G * (Function_G_Square + 3 * Hankel_Var_Square)));

	c2(3, 0) = c2(0, 3);
	c2(3, 1) = c2(1, 3);
	c2(3, 2) = c2(2, 3);
	c2(3, 3) = Complex_Shear_Mod / mydenom * (-Function_G * myK1 * (myQ2 * myQ3 * Function_F_Mul_Function_G - myQ1 * myQ4 * Hankel_Var_Square));

	//Update Stiffness Matrix
	if (mydenom.x != 0 || mydenom.y != 0)
	{
		for (int i = 0; i < 4; i++) {
			for (int j = 0; j < 4; j++) {
				matrix->array_2d(2 * Layer_Number + i, 2 * Layer_Number + j) += c2(i, j);
			}
		}
	}
}

void GlobalStiffMatrix::stiff2node_elastic(             //用于计算弹性层的局部刚度矩阵，考虑材料的频域动态响应.通过 Hankel 变换和拉普拉斯变换求解频域刚度矩阵，最终将结果累加到全局刚度矩阵中
	LayerPavementStructure layer_structure,       //结构体包含层参数
	alglib::complex laplace_var,               //拉普拉斯变量 s=σ+iω
	double gaussian_point,           
	int layer_number)                        //当前层编号
{
	alglib::complex myK, myK1, myQ1, myQ2, myQ3, myQ4, mydenom;
	alglib::complex Hankel_Var, Complex_Poisson, Complex_Density, Complex_Thickness, Complex_Damping;
	alglib::complex Complex_Shear_Mod;
	alglib::complex Complex_Lame;
	alglib::complex C_Wave_Vel;
	alglib::complex S_Wave_Vel;
	alglib::complex Function_F, ehf;
	alglib::complex Function_G, ehg;
	alglib::complex Ctemp;

	alglib::complex_2d_array c2;

	Hankel_Var.x = gaussian_point;           //高斯积分点
	Hankel_Var.y = 0;

	Complex_Poisson.x = layer_structure.possion;          //泊松比
	Complex_Poisson.y = 0;

	Complex_Density.x = layer_structure.density;       //密度
	Complex_Density.y = 0;

	Complex_Thickness.x = layer_structure.thickness;    //厚度
	Complex_Thickness.y = 0;

	Complex_Damping.x = layer_structure.damping;        //阻尼
	Complex_Damping.y = 0;

	Complex_Shear_Mod.x = layer_structure.shaer_mod / 2 / (1 + layer_structure.possion) ;
	Complex_Shear_Mod.y = 0;
	
		//std::cerr << "Complex_Poisson" << Complex_Poisson.x << std::endl;
		//std::cerr << "Complex_Thickness" << Complex_Density.x << std::endl;
		//std::cerr << "LayerPavementStructure layer_structure" << Complex_Thickness.x << std::endl;

		
	Complex_Shear_Mod = Complex_Shear_Mod * (1 + Complex_Damping * laplace_var);            //Complex_Shear_Mod 初始化为弹性剪切模量，并考虑阻尼修正

	Complex_Lame = 2 * Complex_Shear_Mod * Complex_Poisson / (1 - 2 * Complex_Poisson);
	C_Wave_Vel = ViscoWaveEngine::Compute_Power(((Complex_Lame + 2 * Complex_Shear_Mod) / Complex_Density), 0.5);
	S_Wave_Vel = ViscoWaveEngine::Compute_Power((Complex_Shear_Mod / Complex_Density), 0.5);

	alglib::complex Hankel_Var_Square = Hankel_Var * Hankel_Var;
	Function_F = ViscoWaveEngine::Compute_Power((Hankel_Var * Hankel_Var + ((laplace_var * laplace_var) / (C_Wave_Vel * C_Wave_Vel))), 0.5);
	Function_G = ViscoWaveEngine::Compute_Power((Hankel_Var_Square + ((laplace_var * laplace_var) / (S_Wave_Vel * S_Wave_Vel))), 0.5);

	ehf = ViscoWaveEngine::Compute_Exp(-Complex_Thickness * Function_F);
	ehg = ViscoWaveEngine::Compute_Exp(-Complex_Thickness * Function_G);

	alglib::complex Function_F_Square = Function_F * Function_F;
	alglib::complex Function_G_Square = Function_G * Function_G;
	alglib::complex Function_F_Mul_Function_G = Function_F * Function_G;

	myK = Hankel_Var_Square + Function_G_Square;
	myK1 = Hankel_Var_Square - Function_G_Square;

	alglib::complex ehf_square = ehf * ehf;
	alglib::complex ehg_square = ehg * ehg;
	myQ1 = 1 - ehf_square;
	myQ2 = 1 - ehg_square;
	myQ3 = 1 + ehf_square;
	myQ4 = 1 + ehg_square;
	mydenom = myQ1 * myQ2 * (Function_F_Square * Function_G_Square + Hankel_Var_Square * Hankel_Var_Square) - 2 * (myQ3 * myQ4 - 4 * ehf * ehg) * Function_F_Mul_Function_G * Hankel_Var_Square;

	c2.setlength(4, 4);
	c2(0, 0) = Complex_Shear_Mod / mydenom * (-Function_F * myK1 * (myQ1 * myQ4 * Function_F_Mul_Function_G - myQ2 * myQ3 * Hankel_Var_Square));
	c2(0, 1) = Complex_Shear_Mod / mydenom * (-Hankel_Var * (-myQ1 * myQ2 * (2 * Function_F_Square * Function_G_Square + Hankel_Var_Square * myK)
		+ (myQ3 * myQ4 - 4 * ehf * ehg) * Function_F_Mul_Function_G * (Function_G_Square + 3 * Hankel_Var_Square)));
	c2(0, 2) = Complex_Shear_Mod / mydenom * (2 * Function_F * myK1 * (myQ1 * ehg * Function_F_Mul_Function_G - ehf * myQ2 * Hankel_Var_Square));
	c2(0, 3) = Complex_Shear_Mod / mydenom * (-2 * (ehf - ehg) * (1 - ehf * ehg) * Function_F_Mul_Function_G * Hankel_Var * myK1);

	c2(1, 0) = c2(0, 1);
	c2(1, 1) = Complex_Shear_Mod / mydenom * (-Function_G * myK1 * (myQ2 * myQ3 * Function_F_Mul_Function_G - myQ1 * myQ4 * Hankel_Var_Square));
	c2(1, 2) = Complex_Shear_Mod / mydenom * (2 * (ehf - ehg) * (1 - ehf * ehg) * Function_F_Mul_Function_G * Hankel_Var * (Hankel_Var_Square - Function_G_Square));
	c2(1, 3) = Complex_Shear_Mod / mydenom * (-2 * Function_G * myK1 * (-ehf * myQ2 * Function_F_Mul_Function_G + myQ1 * ehg * Hankel_Var_Square));

	c2(2, 0) = c2(0, 2);
	c2(2, 1) = c2(1, 2);
	c2(2, 2) = Complex_Shear_Mod / mydenom * (-Function_F * myK1 * (myQ1 * myQ4 * Function_F_Mul_Function_G - myQ2 * myQ3 * Hankel_Var_Square));
	c2(2, 3) = Complex_Shear_Mod / mydenom * (Hankel_Var * (-myQ1 * myQ2 * (2 * Function_F_Square * Function_G_Square + Hankel_Var_Square * myK)
		+ (myQ3 * myQ4 - 4 * ehf * ehg) * Function_F_Mul_Function_G * (Function_G_Square + 3 * Hankel_Var_Square)));

	c2(3, 0) = c2(0, 3);
	c2(3, 1) = c2(1, 3);
	c2(3, 2) = c2(2, 3);
	c2(3, 3) = Complex_Shear_Mod / mydenom * (-Function_G * myK1 * (myQ2 * myQ3 * Function_F_Mul_Function_G - myQ1 * myQ4 * Hankel_Var_Square));

	// 输出弹性层矩阵到文件
	WriteMatrixToFile(c2, "D:\\temp\\matrix_output.txt", layer_number, gaussian_point, true);
	double epsilon = 1e-12; // 定义阈值
	if (mydenom.x != 0 || mydenom.y != 0)
	{
		for (int i = 0; i < 4; i++) {
			for (int j = 0; j < 4; j++) {
				// 获取当前矩阵元素
				alglib::complex element = c2(i, j);

				// 计算复数模
				const double magnitude = std::sqrt(element.x * element.x + element.y * element.y);

				// 如果模小于阈值则设为0
				if (magnitude < epsilon) {
					element = alglib::complex(0.0, 0.0);
				}
				matrix->array_2d(2 * layer_number + i, 2 * layer_number + j) += c2(i, j);
			}
		}
	}
}


//辅助函数：计算复数绝对值（模长）
double complex_abs(const alglib::complex& z) {
	return std::sqrt(z.x * z.x + z.y * z.y);
}
// 复数三次方程求解（Cardano方法）
std::vector<alglib::complex> solve_cubic(
	alglib::complex a,
	alglib::complex b,
	alglib::complex c,
	alglib::complex d
) {
	std::vector<alglib::complex> roots;
	const double eps = 1e-12;
	// 处理退化情况（a≈0）
	if (complex_abs(a) < eps) {
		if (complex_abs(b) < eps) { // 一次方程
			if (complex_abs(c) < eps) return roots; // 无效方程
			roots.push_back(-d / c);
		}
		else { // 二次方程
			alglib::complex disc = c * c - 4.0 * b * d;
			disc = ViscoWaveEngine::Compute_Power(disc, 0.5);
			roots.push_back((-c + disc) / (2.0 * b));
			roots.push_back((-c - disc) / (2.0 * b));
		}
		return roots;
	}
	// 归一化为 x³ + px² + qx + r = 0
	alglib::complex p = b / a;
	alglib::complex q = c / a;
	alglib::complex r = d / a;
	// 消去二次项得到 y³ + Qy + R = 0
	alglib::complex Q = q - (p * p) / 3.0;
	alglib::complex R = (2.0 * p * p * p) / 27.0 - (p * q) / 3.0 + r;
	// 计算判别式及其平方根
	alglib::complex D = (R * R) / 4.0 + (Q * Q * Q) / 27.0;
	alglib::complex sqrt_D = ViscoWaveEngine::Compute_Power(D, 0.5);
	// 计算立方根参数
	alglib::complex A = (-R) / 2.0 + sqrt_D;
	alglib::complex S = ViscoWaveEngine::Compute_Power(A, 1.0 / 3.0);
	alglib::complex T = -Q / (3.0 * S);  // 确保 S*T = -Q/3
	// 三次单位根
	static const alglib::complex omega(-0.5, std::sqrt(3.0) / 2.0);
	static const alglib::complex omega_sq(-0.5, -std::sqrt(3.0) / 2.0);
	// 计算三个根并转换回原变量
	alglib::complex y0 = S + T;
	alglib::complex y1 = omega * S + omega_sq * T;
	alglib::complex y2 = omega_sq * S + omega * T;
	// 转换为原方程的根
	alglib::complex root_offset = p / 3.0;
	roots.push_back(y0 - root_offset);
	roots.push_back(y1 - root_offset);
	roots.push_back(y2 - root_offset);
	return roots;
}

void GlobalStiffMatrix::stiff1node_elastic(
	LayerPavementStructure layer_structure,
	alglib::complex laplace_var,
	double gaussian_point,
	int layer_number)
{
	
	double  m, d_param;          //拟合参数
	alglib::complex xi, Density, Poisson, Thickness, Damping, Gs;       //路面结构参数
	alglib::complex Sr, n, S_w0, alpha, phi_deg, phi, Kb, Ks1, Kw1, Ka1, rho_s1, rho_w1, rho_a1, Ks, Kw, Ka, rho_s, rho_w, rho_a, Se, k;   //非饱和土体参数
	alglib::complex m_to_ft, kg_to_lbm, Pa_to_psf, g_ftps2, m3_to_ft3, m2_to_ft2, N_to_lbf, g;     //单位换算
	alglib::complex log_term, G_unsat, G_unsat1, pow_Se_1_over_m, term, k_rw_term, k_rw, k_ra, kappa, eta_w, eta_a, k_w, k_a;   //非饱和参数计算变量
	alglib::complex base, A_s, a, chi, A11, A12, A13, A21, A22, A23, b_w, b_a, b11, b12, b13, b21, b22, b23, lambda, rho_eff, b1, b2, b3_term1, b3_term2, b3,
		b31, b32, b33, I1, I2, I3, s2;    //方程系数变量
	alglib::complex  current_lambda, d_n, denominator, f_wn_val, f_un_val, f_an_val, numerator_un, denominator_un, term_rn, f_rn_val, f_zn_val;   //刚度矩阵计算变量
	// ===================== 参数初始化 =====================
	alglib::complex_2d_array c2;

	xi.x = gaussian_point;            //高斯积分点
	xi.y = 0;

	Poisson.x = layer_structure.possion;    //泊松比
	Poisson.y = 0;

	Density.x = layer_structure.density ;     //密度
	Density.y = 0;

	Thickness.x = layer_structure.thickness;   //厚度
	Thickness.y = 0;

	Damping.x = layer_structure.damping;    //阻尼比
	Damping.y = 0;

	Gs.x = layer_structure.shaer_mod;       // 剪切模量/ 2 / (1 + layer_structure.possion) * 
	Gs.y = 0;

	Sr.x = layer_structure.sr;               //饱和度
	Sr.y = 0;

	n.x = 0.40;             //孔隙率
	n.y = 0;

	S_w0.x = 0.05;        //残余饱和度
	S_w0.y = 0;

	alpha.x = 4e-5;      //α参数
	alpha.y = 0;
	m = 0.5;            //拟合参数m
	d_param = 2.0;      //拟合参数d

	phi_deg.x = 27.0;   //内摩擦角（单位度）
	phi_deg.y = 0;

	phi = phi_deg * (3.141592653589793 / 180.0);       // 内摩擦角（弧度）

	Ks.x = 2.96e18;      //土颗粒体积模量单位pa
	Ks.y = 0;

	Kw.x = 0.155e9;     //水体积模量单位pa
	Kw.y = 0;

	Ka.x = 145e3;       //空气体积模量pa
	Ka.y = 0;

	rho_s.x = 1861;     //土颗粒密度参数
	rho_s.y = 0;

	rho_w.x = 1000;     //水密度参数
	rho_w.y = 0;

	rho_a.x = 1.29;     //空气密度
	rho_a.y = 0;

	k.x = 1e-9;    k.y = 0;                               //固有渗透率
	s2 = laplace_var * laplace_var;                      // s²项
	// ====================== 非饱和参数计算 ======================
	Se = (Sr - S_w0) / (1.00 - S_w0);             // 有效饱和度计算
	if (Se.x > 0.99) Se.x = 0.99;                 // 限制上限
	if (Se.x < 0.01) Se.x = 0.01;                 // 限制下限
	// 非饱和剪切模量计算
	alglib::complex log_term_base = ViscoWaveEngine::Compute_Power(Se, -2.0) - 1.0;
	
	log_term = ViscoWaveEngine::Compute_Power(log_term_base, 0.5) + ViscoWaveEngine::Compute_Power(Se, -1.0);     //非饱和参数
	
	G_unsat = Gs +  2200 * ViscoWaveEngine::Compute_Log(log_term)* ViscoWaveEngine::Compute_Tan(phi) / alpha;      // * ViscoWaveEngine::Compute_Tan(phi) / alpha;  // 复数形式
	G_unsat1 = G_unsat * (1 + Damping * laplace_var);
	lambda = (2.0 * G_unsat1 * Poisson) / (1.0 - 2.0 * Poisson); // 拉梅常数λ 
	Kb = (G_unsat1 * 2.0) / 3.0 + lambda;
	// ====================== 渗流参数计算 =====================

	pow_Se_1_over_m = ViscoWaveEngine::Compute_Power(Se, 1.0 / m);                   // 相对渗透率（模型来计算）
	term = 1.0 - pow_Se_1_over_m;
	k_rw_term = ViscoWaveEngine::Compute_Power(term, m);
	k_rw = ViscoWaveEngine::Compute_Power(Se, 0.5) * ViscoWaveEngine::Compute_Power(1.0 - k_rw_term, 2.0);
	k_ra = ViscoWaveEngine::Compute_Power(1.0 - Se, 0.5) * ViscoWaveEngine::Compute_Power(term, 2.0 * m);

	kappa = k ;                // m²固有渗透率转换
	eta_w = 1.005e-3;          // 水动力粘度
	eta_a = 1.5075e-5;         // 空气动力粘度
	g = 9.8;                  // 重力加速度

	k_w = (rho_w * g * kappa * k_rw) / eta_w;            //kw = ρwgκkrw/ηw   - 渗透系数计算
	k_a = (rho_a * g * kappa * k_ra) / eta_a;            //ka = ρagκkra/ηa   - 渗透系数计算
	// ====================== 控制方程系数 ======================
	base = ViscoWaveEngine::Compute_Power(Se, -1.00 / m) - 1.0;               // A_s计算
	A_s = -alpha * m * d_param * (1.0 - S_w0) * ViscoWaveEngine::Compute_Power(Se, (m + 1.0) / m) * ViscoWaveEngine::Compute_Power(base, (d_param - 1.0) / d_param);

	a = 1.0 - (Kb / Ks); // 有效应力参数  a
	chi = Sr;         // χ=Se
	A11 = (a * chi - n * Sr) / Ks - n * A_s + (n * Sr) / Kw;            // 系数矩阵
	A12 = (a * (1.0 - chi) - n * (1.0 - Sr)) * Sr / Ks + n * A_s;
	A13 = (1.0 - Kb / Ks) * Sr;
	A21 = (1.0 - Sr) * (a * chi - n * Sr) / Ks + n * A_s;
	A22 = (1.0 - Sr) * (a * (1.0 - chi) - n * (1.0 - Sr)) / Ks - n * (1.0 - Sr) / Ka - n * A_s;
	A23 = (1.0 - Kb / Ks) * (1.0 - Sr);
	b_w = s2 / (n * Sr) + (g * laplace_var) / k_w;             // 渗流运动方程系数
	b_a = s2 / (n * (1.0 - Sr)) + (g * laplace_var) / k_a;

	b11 = rho_w * b_w * A11;                        // 连续性方程系数  b11 = ρw * bw * A11
	b12 = rho_w * b_w * A12;
	b13 = rho_w * b_w * A13 - rho_w * s2;
	b21 = rho_a * b_a * A21;
	b22 = rho_a * b_a * A22;
	b23 = rho_a * b_a * A23 - rho_a * s2;

	
	rho_eff = (1.0 - n) * rho_s + n * Sr * rho_w + n * (1.0 - Sr) * rho_a;    //总密度ρ = (1 − n)ρs + nSrρw +n(1 − Sr)ρa
	b1 = (-a * chi) + (s2 / b_w);                         // 计算b1、b2、b3
	b2 = (-a * (1.0 - chi)) + (s2 / b_a);
	b3_term1 = (rho_w * s2 * s2) / b_w;                      // rho_w * s^4 / b_w
	b3_term2 = (rho_a * s2 * s2) / b_a;                       // rho_a * s^4 / b_a
	b3 = -(rho_eff * s2) + b3_term1 + b3_term2;

	denominator = lambda + 2 * G_unsat1;         // 计算b31, b32, b33
	b31 = -(b1 * b11 + b2 * b21) / denominator;
	b32 = -(b1 * b12 + b2 * b22) / denominator;
	b33 = -(b1 * b13 + b2 * b23 + b3) / denominator;
	// ====================== 特征方程求解 ======================
	I1 = b11 + b12 + b13;                                          // 特征方程系数
	I2 = b11 * b22 + b22 * b33 + b33 * b11 - (b12 * b21 + b23 * b32 + b31 * b13);
	I3 = b11 * b22 * b33 + b12 * b23 * b31 + b21 * b32 * b13 - (b11 * b23 * b32 + b22 * b31 * b13 + b33 * b12 * b21);

	std::vector<alglib::complex> roots = solve_cubic(1.0, -I1, I2, -I3);          // 求解三次方程
	//auto temproots = solve_cubic(1.0, alglib::complex(-3,-1), alglib::complex(2, 3), alglib::complex(0, -2));    验证三次方程的求解。

	// ====================== 刚度矩阵计算 ======================
	std::vector<alglib::complex> f_un, f_wn, f_an, f_rn, f_zn, lambda_n;
	
	for (size_t i = 0; i < roots.size(); ++i) {                   // 遍历每个根计算系数
		const alglib::complex& root = roots[i];

		current_lambda = ViscoWaveEngine::Compute_Power((root + xi * xi), 0.5); //  λn
		
		
		d_n = root;

		denominator = d_n * d_n - (b11 + b12) * d_n + (b11 * b22 - b12 * b21);
		const double eps = 1e-12;
		if (std::abs(denominator.x) < eps && std::abs(denominator.y) < eps) {
			denominator = alglib::complex(eps, eps);
		}

		// 计算各系数
		f_wn_val = (b13 * d_n - b13 * b22 + b12 * b23) / denominator;
		f_an_val = (b23 * d_n - b11 * b23 + b13 * b21) / denominator;

		numerator_un = -current_lambda * ((lambda + G_unsat1) + b1 * f_wn_val + b2 * f_an_val);
		denominator_un = (G_unsat1 * (current_lambda * current_lambda)) - (G_unsat1 * (xi * xi)) + b3;
		if (std::abs(denominator_un.x) < eps && std::abs(denominator_un.y) < eps) {
			denominator_un = alglib::complex(eps, eps);
		}
		f_un_val = numerator_un / denominator_un;

		term_rn = (current_lambda - f_un_val * (current_lambda * current_lambda + xi * xi)) / xi;
		f_rn_val = G_unsat1 * term_rn;

		f_zn_val = 2.0 * G_unsat1 * f_un_val * current_lambda + lambda - a * chi * f_wn_val - a * (1.0 - chi) * f_an_val;

		
		f_un.push_back(f_un_val);                               // 存储系数
		f_wn.push_back(f_wn_val);
		f_an.push_back(f_an_val);
		f_rn.push_back(f_rn_val);
		f_zn.push_back(f_zn_val);
		lambda_n.push_back(current_lambda);
	}

	alglib::complex sum_f_un(0.0, 0.0);
	alglib::complex sum_f_wn(0.0, 0.0);
	alglib::complex sum_f_an(0.0, 0.0);
	alglib::complex sum_f_rn(0.0, 0.0);
	alglib::complex sum_f_zn(0.0, 0.0);
	alglib::complex sum_1_minus_f_un_lambda_n(0.0, 0.0);
	for (size_t i = 0; i < roots.size(); ++i) {
		sum_f_un += f_un[i];
		sum_f_wn += f_wn[i];
		sum_f_an += f_an[i];
		sum_f_rn += f_rn[i];
		sum_f_zn += f_zn[i];
		sum_1_minus_f_un_lambda_n += (1.0 - f_un[i] * lambda_n[i]);
	}
	// ====================== 构建S1和S2矩阵 ======================
	alglib::complex_2d_array A1, B2;
	A1.setlength(2, 2);
	B2.setlength(2, 2);

	// --- 关键参数计算 ---
	alglib::complex lambda_0 = ViscoWaveEngine::Compute_Power((xi * xi - (b3 / G_unsat1)), 0.5);    //λ0
	
	// --- S1矩阵构建
	A1[0][0] = -lambda_0 / xi;                                        // -λ₀/ξ
	A1[0][1] = (1.00 / xi) * sum_1_minus_f_un_lambda_n;              // (1/ξ)Σ(1-f_unλ_n)
	A1[1][0] = -1.00;                                               // -1
	A1[1][1] = -sum_f_un;                                            // -Σf_un

	// --- S2矩阵构建
	B2[0][0] = G_unsat1 * (2.00 * xi - b3 / (G_unsat1 * xi));          // G(2ξ - b3/(Gξ))
	B2[0][1] = -sum_f_rn;                                           // -Σf_rn
	B2[1][0] = 2.00 * G_unsat1 * lambda_0;                            // 2Gλ₀
	B2[1][1] = sum_f_zn;                                            // Σf_zn
	alglib::complex fenmu_juzhen = A1[0][0] * A1[1][1] - A1[0][1] * A1[1][0];
	// ====================== 基于矩阵元素的构建 ======================
	c2.setlength(2, 2);
	c2(0, 0) = (B2[0][1] * A1[1][0] - B2[0][0] * A1[1][1]) / fenmu_juzhen;
	c2(0, 1) = (B2[0][0] * A1[0][1] - B2[0][1] * A1[0][0]) / fenmu_juzhen;
	c2(1, 0) = (B2[1][1] * A1[1][0] - B2[1][0] * A1[1][1]) / fenmu_juzhen;
	c2(1, 1) = (B2[1][0] * A1[0][1] - B2[1][1] * A1[0][0]) / fenmu_juzhen;
	// ===================== 装配到全局矩阵 =====================
	

	if (fenmu_juzhen.x != 0 || fenmu_juzhen.y != 0)
	{
		for (int i = 0; i < 2; ++i) {
			for (int j = 0; j < 2; ++j) {				
				matrix->array_2d(2 * layer_number + i, 2 * layer_number + j) +=  c2[i][j];
				WriteMatrixToFile(c2, "D:\\temp\\Feibaohe-matrix_output.txt", layer_number, gaussian_point, false);
			}

		}

	}
}

/**void GlobalStiffMatrix::stiff1node_elastic(           //计算半空间底层（厚度为0）的局部刚度矩阵，用于描述半无限弹性体的动态响应
	LayerPavementStructure layer_structure,     //层参数（厚度、泊松比、密度、剪切模量、阻尼
	alglib::complex laplace_var,           //拉普拉斯变量
	double gaussian_point,                //高斯积分点
	int layer_number)                     //当前层编号   用于定位全矩阵的位置
{
	alglib::complex myK, mydenom;
	alglib::complex Hankel_Var, Complex_Poisson, Complex_Density, Complex_Thickness, Complex_Damping;
	alglib::complex Complex_Shear_Mod;
	alglib::complex Complex_Lame;
	alglib::complex C_Wave_Vel;
	alglib::complex S_Wave_Vel;
	alglib::complex Function_F;
	alglib::complex Function_G;
	alglib::complex Ctemp;

	alglib::complex_2d_array c2;

	Hankel_Var.x = gaussian_point;
	Hankel_Var.y = 0;

	Complex_Poisson.x = layer_structure.possion;
	Complex_Poisson.y = 0;

	Complex_Density.x = layer_structure.density;
	Complex_Density.y = 0;

	Complex_Thickness.x = layer_structure.thickness;
	Complex_Thickness.y = 0;

	Complex_Damping.x = layer_structure.damping;
	Complex_Damping.y = 0;

	Complex_Shear_Mod.x = layer_structure.shaer_mod / 2.00 / (1 + layer_structure.possion) ;
	Complex_Shear_Mod.y = 0;
	Complex_Shear_Mod = Complex_Shear_Mod * (1 + Complex_Damping * laplace_var);

	Complex_Lame = 2 * Complex_Shear_Mod * Complex_Poisson / (1 - 2 * Complex_Poisson);
	C_Wave_Vel = ViscoWaveEngine::Compute_Power(((Complex_Lame + 2 * Complex_Shear_Mod) / Complex_Density), 0.5);
	S_Wave_Vel = ViscoWaveEngine::Compute_Power((Complex_Shear_Mod / Complex_Density), 0.5);

	alglib::complex Hankel_Var_Square = Hankel_Var * Hankel_Var;
	Function_F = ViscoWaveEngine::Compute_Power((Hankel_Var_Square + ((laplace_var * laplace_var) / (C_Wave_Vel * C_Wave_Vel))), 0.5);
	Function_G = ViscoWaveEngine::Compute_Power((Hankel_Var_Square + ((laplace_var * laplace_var) / (S_Wave_Vel * S_Wave_Vel))), 0.5);

	alglib::complex Function_G_Square = Function_G * Function_G;
	alglib::complex Function_F_Mul_Function_G = Function_F * Function_G;

	myK = (Hankel_Var_Square + Function_G_Square);
	mydenom = (Hankel_Var_Square - Function_F_Mul_Function_G);

	c2.setlength(2, 2);
	c2(0, 0) = Complex_Shear_Mod / mydenom * (2 * Hankel_Var_Square * Function_F - Function_F * myK);
	c2(0, 1) = Complex_Shear_Mod / mydenom * (-2 * Hankel_Var * Function_F_Mul_Function_G + Hankel_Var * myK);
	c2(1, 0) = c2(0, 1);
	c2(1, 1) = Complex_Shear_Mod / mydenom * (2 * Hankel_Var_Square * Function_G - Function_G * myK);
	WriteMatrixToFile(c2, "D:\\桌面\\Tanxinghe-matrix_output.txt", layer_number, gaussian_point, false);
	if (mydenom.x != 0 || mydenom.y != 0)
	{
		for (int i = 0; i < 2; i++) {
			for (int j = 0; j < 2; j++) {
				matrix->array_2d(2 * layer_number + i, 2 * layer_number + j) += c2(i, j);
			}
		}
	}
}**/

void GlobalStiffMatrix::stiff1node_elastic_nohalf(              //  计算有限厚度底层的局部刚度矩阵， 
	LayerPavementStructure layer_structure,                  //层参数（厚度、泊松比、密度、剪切模量、阻尼
	alglib::complex laplace_var,                     //拉普拉斯变量
	double gaussian_point,                             //高斯积分点
	int layer_number)                             //层编号
{
	
	alglib::complex myK, myK1, myQ1, myQ2, myQ3, myQ4, mydenom;
	alglib::complex Hankel_Var, Complex_Poisson, Complex_Density, Complex_Thickness, Complex_Damping;
	alglib::complex Complex_Shear_Mod;
	alglib::complex Complex_Lame;
	alglib::complex C_Wave_Vel;
	alglib::complex S_Wave_Vel;
	alglib::complex Function_F, ehf;
	alglib::complex Function_G, ehg;
	alglib::complex Ctemp;

	alglib::complex_2d_array c2;

	Hankel_Var.x = gaussian_point;
	Hankel_Var.y = 0;

	Complex_Poisson.x = layer_structure.possion;
	Complex_Poisson.y = 0;

	Complex_Density.x = layer_structure.density;
	Complex_Density.y = 0;

	Complex_Thickness.x = layer_structure.thickness;
	Complex_Thickness.y = 0;

	Complex_Damping.x = layer_structure.damping;
	Complex_Damping.y = 0;

	Complex_Shear_Mod.x = layer_structure.shaer_mod;
	Complex_Shear_Mod.y = 0;
	Complex_Shear_Mod = Complex_Shear_Mod * (1 + Complex_Damping * laplace_var);

	Complex_Lame = 2 * Complex_Shear_Mod * Complex_Poisson / (1 - 2 * Complex_Poisson);
	C_Wave_Vel = ViscoWaveEngine::Compute_Power(((Complex_Lame + 2 * Complex_Shear_Mod) / Complex_Density), 0.5);
	S_Wave_Vel = ViscoWaveEngine::Compute_Power((Complex_Shear_Mod / Complex_Density), 0.5);

	alglib::complex Hankel_Var_Square = Hankel_Var * Hankel_Var;
	Function_F = ViscoWaveEngine::Compute_Power((Hankel_Var_Square + ((laplace_var * laplace_var) / (C_Wave_Vel * C_Wave_Vel))), 0.5);
	Function_G = ViscoWaveEngine::Compute_Power((Hankel_Var_Square + ((laplace_var * laplace_var) / (S_Wave_Vel * S_Wave_Vel))), 0.5);

	ehf = ViscoWaveEngine::Compute_Exp(-Complex_Thickness * Function_F);
	ehg = ViscoWaveEngine::Compute_Exp(-Complex_Thickness * Function_G);

	alglib::complex Function_F_Square = Function_F * Function_F;
	alglib::complex Function_G_Square = Function_G * Function_G;
	alglib::complex Function_F_Mul_Function_G = Function_F * Function_G;

	myK = (Hankel_Var_Square + Function_G_Square);
	myK1 = (Hankel_Var_Square - Function_G_Square);

	alglib::complex ehf_square = ehf * ehf;
	alglib::complex ehg_square = ehg * ehg;
	myQ1 = 1 - ehf_square;
	myQ2 = 1 - ehg_square;
	myQ3 = 1 + ehf_square;
	myQ4 = 1 + ehg_square;
	mydenom = myQ1 * myQ2 * (Function_F_Square * Function_G_Square + Hankel_Var_Square * Hankel_Var_Square) - 2 * (myQ3 * myQ4 - 4 * ehf * ehg) * Function_F_Mul_Function_G * Hankel_Var_Square;

	c2.setlength(4, 4);
	c2(0, 0) = Complex_Shear_Mod / mydenom * (-Function_F * myK1 * (myQ1 * myQ4 * Function_F_Mul_Function_G - myQ2 * myQ3 * Hankel_Var_Square));
	c2(0, 1) = Complex_Shear_Mod / mydenom * (-Hankel_Var * (-myQ1 * myQ2 * (2 * Function_F_Square * Function_G_Square + Hankel_Var_Square * myK) + (myQ3 * myQ4 - 4 * ehf * ehg) * Function_F_Mul_Function_G * (Function_G_Square + 3 * Hankel_Var_Square)));

	c2(1, 0) = c2(0, 1);
	c2(1, 1) = Complex_Shear_Mod / mydenom * (-Function_G * myK1 * (myQ2 * myQ3 * Function_F_Mul_Function_G - myQ1 * myQ4 * Hankel_Var_Square));

	if (mydenom.x != 0 || mydenom.y != 0)
	{
		for (int i = 0; i < 2; i++) {
			for (int j = 0; j < 2; j++) {
				matrix->array_2d(2 * layer_number + i, 2 * layer_number + j) += c2(i, j);
			}
		}
	}
}
