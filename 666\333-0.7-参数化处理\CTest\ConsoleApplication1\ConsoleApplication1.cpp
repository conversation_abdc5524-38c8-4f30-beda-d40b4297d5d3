// ConsoleApplication1.cpp : 此文件包含 "main" 函数。程序执行将在此处开始并结束。
//
#include <windows.h>
#include <iostream>
#include <fstream>
#include <string>
#include <iomanip>  // 用于控制输出格式
#include <cmath>    // 用于sin函数
#include <memory>   // 用于智能指针
#include <chrono>   // 添加时间库

typedef int (*ViscoWave)(double* displacement, double* Sigmoid,double* Pavement,
	double Load_Pressure, double Load_Radius, double* Sensor_Location,double* Time,
    double* Timehistory, double dt, int num_prony_elements, int Num_Pavt_Layers, int Num_Sensors,
	int Num_Time, int Num_VE_Layer);
int main()
{
    auto start_time = std::chrono::high_resolution_clock::now();

    HMODULE hDll = LoadLibrary(L"ViscoWave_x64.dll");     //调用DLL文件进行计算
    if (hDll == NULL) {

        std::cout << "Failed to load DLL." << std::endl;

        return 1;

    }

    ViscoWave viscoWave = (ViscoWave)GetProcAddress(hDll, "ViscoWave"); // 获取DLL中导出函数的地址

    if (viscoWave == NULL) {

        std::cout << "Failed to get function address." << std::endl;

        FreeLibrary(hDll); // 释放DLL

        return 1;

    }

    int Num_Time = 351;
    int Num_Sensors = 9;
    double* displacement = (double*)calloc(Num_Sensors * Num_Time, sizeof(double));
    double* Sigmoid = (double*)calloc(4, sizeof(double));
    Sigmoid[0] = 4.85047;      //sigmod函数参数  Sigmoid(j, 1) = Sigmoid(j, 1) + WorksheetFunction.Log10(144 / 2 / (1 + layerpara(j, 2)))
    Sigmoid[1] = 3.44625;
    Sigmoid[2] = -0.128029;
    Sigmoid[3] = 0.554349;
    double* Pavement = (double*)calloc(16, sizeof(double));
    Pavement[0] = 2300000000.00;                      // 模量：弹性模量
    Pavement[1] = 0.25;                               //泊松比
    Pavement[2] = 2100.0;                            //密度：kg/m^3
    Pavement[3] = 0.180;                             //厚度 ：m
    Pavement[4] = 0.001;                             //阻尼：0.1%
    
    //1层的路面结构参数：

    Pavement[5] = 901000000.00;                    // 模量：弹性模量
    // Pavement[5] =   5000000000.00;                    // 模量：弹性模量
    Pavement[6] = 0.25;                            // 泊松比
    Pavement[7] = 2000.00;                            // 密度：kg/m^3
    Pavement[8] = 0.350;                             // 厚度：m
    Pavement[9] = 0.001;                             //阻尼：0.1%
    // 2层的路面结构参数：

    Pavement[10] = 80000000.00;               // 模量：弹性模量
    Pavement[11] = 0.35;                       // 泊松比
    Pavement[12] = 1601.85;                  // 密度：kg/m^3
    Pavement[13] = 0.00;                        //厚度：厚度为0 按半空间处理计算
    Pavement[14] = 0.001;                        //阻尼：0.1%
    Pavement[15] = 0.10;
    // 三层的路面结构参数
    double Load_Pressure = 707000;              // 0.707Mpa为FWD加载荷载      
    double Load_Radius = 0.150;                 // 半径  0.15m
    double* Sensor_Location = (double*)calloc(9, sizeof(double));
   
    //传感器位置单位m
    Sensor_Location[0] = 0;             
    Sensor_Location[1] = 0.203;          
    Sensor_Location[2] = 0.305;          
    Sensor_Location[3] = 0.457;          
    Sensor_Location[4] = 0.610;          
    Sensor_Location[5] = 0.914;          
    Sensor_Location[6] = 1.219;          
    Sensor_Location[7] = 1.524;            
    Sensor_Location[8] = 1.829;          
  

    double dt = 0.0002;      // 时间间隔
    double* Time = (double*)calloc(Num_Time, sizeof(double));
    for (int i = 0; i < (Num_Time > 300 ? 300 : Num_Time); i++) {
        Time[i] = i * dt;

    }
    double* Timehistory = (double*)calloc(Num_Time, sizeof(double));  //  荷载时间历程 
    Timehistory[0] = 0;
    Timehistory[1] = 0;
    Timehistory[2] = 0;
    Timehistory[3] = 0;
    Timehistory[4] = 0;
    Timehistory[5] = 0;
    Timehistory[6] = 0;
    Timehistory[7] = 0;
    Timehistory[8] = 0;
    Timehistory[9] = 0;
    Timehistory[10] = 0;
    Timehistory[11] = 0;
    Timehistory[12] = 0;
    Timehistory[13] = 0;
    Timehistory[14] = 0;
    Timehistory[15] = 0;
    Timehistory[16] = 0;
    Timehistory[17] = 0;
    Timehistory[18] = 0;
    Timehistory[19] = 0;
    Timehistory[20] = 0;
    Timehistory[21] = 0;
    Timehistory[22] = 0;
    Timehistory[23] = 0;
    Timehistory[24] = 0;
    Timehistory[25] = 0;
    Timehistory[26] = 0.0251321;
    Timehistory[27] = 0.0502483;
    Timehistory[28] = 0.0753328;
    Timehistory[29] = 0.10037;
    Timehistory[30] = 0.125343;
    Timehistory[31] = 0.150237;
    Timehistory[32] = 0.175037;
    Timehistory[33] = 0.199726;
    Timehistory[34] = 0.224288;
    Timehistory[35] = 0.24871;
    Timehistory[36] = 0.272973;
    Timehistory[37] = 0.297065;
    Timehistory[38] = 0.320969;
    Timehistory[39] = 0.34467;
    Timehistory[40] = 0.368154;
    Timehistory[41] = 0.391405;
    Timehistory[42] = 0.414408;
    Timehistory[43] = 0.43715;
    Timehistory[44] = 0.459616;
    Timehistory[45] = 0.481792;
    Timehistory[46] = 0.503663;
    Timehistory[47] = 0.525216;
    Timehistory[48] = 0.546437;
    Timehistory[49] = 0.567314;
    Timehistory[50] = 0.587832;
    Timehistory[51] = 0.607978;
    Timehistory[52] = 0.627741;
    Timehistory[53] = 0.647107;
    Timehistory[54] = 0.666064;
    Timehistory[55] = 0.684601;
    Timehistory[56] = 0.702705;
    Timehistory[57] = 0.720366;
    Timehistory[58] = 0.737571;
    Timehistory[59] = 0.754311;
    Timehistory[60] = 0.770574;
    Timehistory[61] = 0.786351;
    Timehistory[62] = 0.80163;
    Timehistory[63] = 0.816404;
    Timehistory[64] = 0.830661;
    Timehistory[65] = 0.844395;
    Timehistory[66] = 0.857594;
    Timehistory[67] = 0.870252;
    Timehistory[68] = 0.882361;
    Timehistory[69] = 0.893912;
    Timehistory[70] = 0.904898;
    Timehistory[71] = 0.915313;
    Timehistory[72] = 0.92515;
    Timehistory[73] = 0.934403;
    Timehistory[74] = 0.943065;
    Timehistory[75] = 0.951132;
    Timehistory[76] = 0.958597;
    Timehistory[77] = 0.965458;
    Timehistory[78] = 0.971708;
    Timehistory[79] = 0.977345;
    Timehistory[80] = 0.982365;
    Timehistory[81] = 0.986764;
    Timehistory[82] = 0.99054;
    Timehistory[83] = 0.99369;
    Timehistory[84] = 0.996212;
    Timehistory[85] = 0.998106;
    Timehistory[86] = 0.999368;
    Timehistory[87] = 1;
    Timehistory[88] = 1;
    Timehistory[89] = 0.999368;
    Timehistory[90] = 0.998106;
    Timehistory[91] = 0.996212;
    Timehistory[92] = 0.99369;
    Timehistory[93] = 0.99054;
    Timehistory[94] = 0.986764;
    Timehistory[95] = 0.982365;
    Timehistory[96] = 0.977345;
    Timehistory[97] = 0.971708;
    Timehistory[98] = 0.965458;
    Timehistory[99] = 0.958597;
    Timehistory[100] = 0.951132;
    Timehistory[101] = 0.943065;
    Timehistory[102] = 0.934403;
    Timehistory[103] = 0.92515;
    Timehistory[104] = 0.915313;
    Timehistory[105] = 0.904898;
    Timehistory[106] = 0.893912;
    Timehistory[107] = 0.882361;
    Timehistory[108] = 0.870252;
    Timehistory[109] = 0.857594;
    Timehistory[110] = 0.844395;
    Timehistory[111] = 0.830661;
    Timehistory[112] = 0.816404;
    Timehistory[113] = 0.80163;
    Timehistory[114] = 0.786351;
    Timehistory[115] = 0.770574;
    Timehistory[116] = 0.754311;
    Timehistory[117] = 0.737571;
    Timehistory[118] = 0.720366;
    Timehistory[119] = 0.702705;
    Timehistory[120] = 0.684601;
    Timehistory[121] = 0.666064;
    Timehistory[122] = 0.647107;
    Timehistory[123] = 0.627741;
    Timehistory[124] = 0.607978;
    Timehistory[125] = 0.587832;
    Timehistory[126] = 0.567314;
    Timehistory[127] = 0.546437;
    Timehistory[128] = 0.525216;
    Timehistory[129] = 0.503663;
    Timehistory[130] = 0.481792;
    Timehistory[131] = 0.459616;
    Timehistory[132] = 0.43715;
    Timehistory[133] = 0.414408;
    Timehistory[134] = 0.391405;
    Timehistory[135] = 0.368154;
    Timehistory[136] = 0.34467;
    Timehistory[137] = 0.320969;
    Timehistory[138] = 0.297065;
    Timehistory[139] = 0.272973;
    Timehistory[140] = 0.24871;
    Timehistory[141] = 0.224288;
    Timehistory[142] = 0.199726;
    Timehistory[143] = 0.175037;
    Timehistory[144] = 0.150237;
    Timehistory[145] = 0.125343;
    Timehistory[146] = 0.10037;
    Timehistory[147] = 0.0753328;
    Timehistory[148] = 0.0502483;
    Timehistory[149] = 0.0251321;
    Timehistory[150] = 1.22525e-16;
    Timehistory[151] = 0;
    Timehistory[152] = 0;
    Timehistory[153] = 0;
    Timehistory[154] = 0;
    Timehistory[155] = 0;
    Timehistory[156] = 0;
    Timehistory[157] = 0;
    Timehistory[158] = 0;
    Timehistory[159] = 0;
    Timehistory[160] = 0;
    Timehistory[161] = 0;
    Timehistory[162] = 0;
    Timehistory[163] = 0;
    Timehistory[164] = 0;
    Timehistory[165] = 0;
    Timehistory[166] = 0;
    Timehistory[167] = 0;
    Timehistory[168] = 0;
    Timehistory[169] = 0;
    Timehistory[170] = 0;
    Timehistory[171] = 0;
    Timehistory[172] = 0;
    Timehistory[173] = 0;
    Timehistory[174] = 0;
    Timehistory[175] = 0;
    Timehistory[176] = 0;
    Timehistory[177] = 0;
    Timehistory[178] = 0;
    Timehistory[179] = 0;
    Timehistory[180] = 0;
    Timehistory[181] = 0;
    Timehistory[182] = 0;
    Timehistory[183] = 0;
    Timehistory[184] = 0;
    Timehistory[185] = 0;
    Timehistory[186] = 0;
    Timehistory[187] = 0;
    Timehistory[188] = 0;
    Timehistory[189] = 0;
    Timehistory[190] = 0;
    Timehistory[191] = 0;
    Timehistory[192] = 0;
    Timehistory[193] = 0;
    Timehistory[194] = 0;
    Timehistory[195] = 0;
    Timehistory[196] = 0;
    Timehistory[197] = 0;
    Timehistory[198] = 0;
    Timehistory[199] = 0;
    Timehistory[200] = 0;
    Timehistory[201] = 0;
    Timehistory[202] = 0;
    Timehistory[203] = 0;
    Timehistory[204] = 0;
    Timehistory[205] = 0;
    Timehistory[206] = 0;
    Timehistory[207] = 0;
    Timehistory[208] = 0;
    Timehistory[209] = 0;
    Timehistory[210] = 0;
    Timehistory[211] = 0;
    Timehistory[212] = 0;
    Timehistory[213] = 0;
    Timehistory[214] = 0;
    Timehistory[215] = 0;
    Timehistory[216] = 0;
    Timehistory[217] = 0;
    Timehistory[218] = 0;
    Timehistory[219] = 0;
    Timehistory[220] = 0;
    Timehistory[221] = 0;
    Timehistory[222] = 0;
    Timehistory[223] = 0;
    Timehistory[224] = 0;
    Timehistory[225] = 0;
    Timehistory[226] = 0;
    Timehistory[227] = 0;
    Timehistory[228] = 0;
    Timehistory[229] = 0;
    Timehistory[230] = 0;
    Timehistory[231] = 0;
    Timehistory[232] = 0;
    Timehistory[233] = 0;
    Timehistory[234] = 0;
    Timehistory[235] = 0;
    Timehistory[236] = 0;
    Timehistory[237] = 0;
    Timehistory[238] = 0;
    Timehistory[239] = 0;
    Timehistory[240] = 0;
    Timehistory[241] = 0;
    Timehistory[242] = 0;
    Timehistory[243] = 0;
    Timehistory[244] = 0;
    Timehistory[245] = 0;
    Timehistory[246] = 0;
    Timehistory[247] = 0;
    Timehistory[248] = 0;
    Timehistory[249] = 0;
    Timehistory[250] = 0;
    Timehistory[251] = 0;
    Timehistory[252] = 0;
    Timehistory[253] = 0;
    Timehistory[254] = 0;
    Timehistory[255] = 0;
    Timehistory[256] = 0;
    Timehistory[257] = 0;
    Timehistory[258] = 0;
    Timehistory[259] = 0;
    Timehistory[260] = 0;
    Timehistory[261] = 0;
    Timehistory[262] = 0;
    Timehistory[263] = 0;
    Timehistory[264] = 0;
    Timehistory[265] = 0;
    Timehistory[266] = 0;
    Timehistory[267] = 0;
    Timehistory[268] = 0;
    Timehistory[269] = 0;
    Timehistory[270] = 0;
    Timehistory[271] = 0;
    Timehistory[272] = 0;
    Timehistory[273] = 0;
    Timehistory[274] = 0;
    Timehistory[275] = 0;
    Timehistory[276] = 0;
    Timehistory[277] = 0;
    Timehistory[278] = 0;
    Timehistory[279] = 0;
    Timehistory[280] = 0;
    Timehistory[281] = 0;
    Timehistory[282] = 0;
    Timehistory[283] = 0;
    Timehistory[284] = 0;
    Timehistory[285] = 0;
    Timehistory[286] = 0;
    Timehistory[287] = 0;
    Timehistory[288] = 0;
    Timehistory[289] = 0;
    Timehistory[290] = 0;
    Timehistory[291] = 0;
    Timehistory[292] = 0;
    Timehistory[293] = 0;
    Timehistory[294] = 0;
    Timehistory[295] = 0;
    Timehistory[296] = 0;
    Timehistory[297] = 0;
    Timehistory[298] = 0;
    Timehistory[299] = 0;
    Timehistory[300] = 0;
    Timehistory[301] = 0;
    Timehistory[302] = 0;
    Timehistory[303] = 0;
    Timehistory[304] = 0;
    Timehistory[305] = 0;
    Timehistory[306] = 0;
    Timehistory[307] = 0;
    Timehistory[308] = 0;
    Timehistory[309] = 0;
    Timehistory[310] = 0;
    Timehistory[311] = 0;
    Timehistory[312] = 0;
    Timehistory[313] = 0;
    Timehistory[314] = 0;
    Timehistory[315] = 0;
    Timehistory[316] = 0;
    Timehistory[317] = 0;
    Timehistory[318] = 0;
    Timehistory[319] = 0;
    Timehistory[320] = 0;
    Timehistory[321] = 0;
    Timehistory[322] = 0;
    Timehistory[323] = 0;
    Timehistory[324] = 0;
    Timehistory[325] = 0;
    Timehistory[326] = 0;
    Timehistory[327] = 0;
    Timehistory[328] = 0;
    Timehistory[329] = 0;
    Timehistory[330] = 0;
    Timehistory[331] = 0;
    Timehistory[332] = 0;
    Timehistory[333] = 0;
    Timehistory[334] = 0;
    Timehistory[335] = 0;
    Timehistory[336] = 0;
    Timehistory[337] = 0;
    Timehistory[338] = 0;
    Timehistory[339] = 0;
    Timehistory[340] = 0;
    Timehistory[341] = 0;
    Timehistory[342] = 0;
    Timehistory[343] = 0;
    Timehistory[344] = 0;
    Timehistory[345] = 0;
    Timehistory[346] = 0;
    Timehistory[347] = 0;
    Timehistory[348] = 0;
    Timehistory[349] = 0;
    Timehistory[350] = 0;

    int num_prony_elements = 1;
    int Num_Pavt_Layers=3;
    int Num_VE_Layer=1;

    std::ofstream outFile("d:/out.txt");
    if (!outFile.is_open()) {
        std::cerr << "Error opening file" << std::endl;
        return -1;
    }

    std::cout << "开始执行ViscoWave计算..." << std::endl;
    auto calc_start = std::chrono::high_resolution_clock::now();

    int result = viscoWave(displacement, Sigmoid, Pavement,Load_Pressure,  Load_Radius, Sensor_Location, Time,
        Timehistory,dt, num_prony_elements, Num_Pavt_Layers, Num_Sensors,Num_Time,  Num_VE_Layer); // 调用DLL中的函数
    /*
    for (int i = 0; i < Num_Sensors; i++) {     //循环变量  i：遍历所有传感器，j：遍历所有时间点。
        for (int j = 0; j < Num_Time; j++) {
            outFile << i << "," << j << "," << displacement[i * Num_Time + j] << std::endl;  //将 output_disp_matrix(j, i) 的值存储到 displacement[i * Num_Time + j] 中
        }
    }*/

    auto calc_end = std::chrono::high_resolution_clock::now();

    // 添加文件保存提示
    std::cout << "保存结果到文件..." << std::endl;

    // 添加CSV表头
    outFile << "Time(s)";  // 时间列
    for (int s = 0; s < Num_Sensors; ++s) {
        outFile << ",R" << Sensor_Location[s] << "(mm)";  // 9个传感器列 ，索引数据，输出时进行单位的转换。
    }
    outFile << "\n";  // 换行

    for (int t = 0; t < 300; ++t) {
        outFile << std::fixed << std::setprecision(6) << Time[t];
        for (int s = 0; s < Num_Sensors; ++s) {
            outFile << "," << std::scientific << std::setprecision(6)
                << displacement[s * Num_Time + t]*1000;         //输出单位转换为mm
        }
        outFile << "\n";
    }
    // 计算总耗时
    auto end_time = std::chrono::high_resolution_clock::now();

    // 转换时间单位
    auto total_duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    auto calc_duration = std::chrono::duration_cast<std::chrono::milliseconds>(calc_end - calc_start);

    // 显示运行时间
    std::cout << "\n----- 执行摘要 -----" << std::endl;
    std::cout << "总执行时间: "
        << total_duration.count() / 1000.0
        << " 秒" << std::endl;
    std::cout << "  核心计算时间: "
        << calc_duration.count() / 1000.0
        << " 秒" << std::endl;
    std::cout << "  数据保存时间: "
        << (total_duration - calc_duration).count() / 1000.0
        << " 秒" << std::endl;


   /**//*for (int i = 0; i < Num_Sensors; i++) {
        /*
        std::cout << "displacement: " << displacement[i]*12000.00 << std::endl;
        std::cout << "Sigmoid: " << Sigmoid[i] << std::endl;
        std::cout << "Pavement: " << Pavement[i] << std::endl;
        std::cout << "Sensor_Location: " << Sensor_Location[i] << std::endl;
        std::cout << "Time: " << Time[i] << std::endl;
        std::cout << "Timehistory: " << Timehistory[i] << std::endl;
        */
       // outFile << (displacement[i] * 12000.00) << "," << Sigmoid[i] << "," << Pavement[i] << "," << Sensor_Location[i] << "," << Time[i] << "," << Timehistory[i] << std::endl; // 写入文本到文件
  //*}*//*

    outFile.close(); // 关闭文件流
    free(displacement);
    free(Sigmoid);
    free(Pavement);
    free(Sensor_Location);
    free(Time);
    free(Timehistory);
    std::cout << "Result: " << result << std::endl;
    
    FreeLibrary(hDll); // 释放DLL

    std::cout << "Hello World!\n";
}
